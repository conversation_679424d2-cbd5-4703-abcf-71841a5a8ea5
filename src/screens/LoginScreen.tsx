/**TODO: Drop it later
 *
 * Simple Login Screen
 *
 * Verifies email address against UNA via middleware-api and retrieves user profile
 */

import React, { useState } from 'react';
import {
	View,
	Text,
	TextInput,
	TouchableOpacity,
	StyleSheet,
	ActivityIndicator,
	KeyboardAvoidingView,
	Platform,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { router } from 'expo-router';
import {
	devLoginUser,
	selectIsLoading,
	selectError,
	clearError,
} from '@/features/user/userSlice';
import { useTheme } from '@/theme';
import type { AppDispatch } from '@/store';
import { showAlert } from '@/utils/alert';
import config from '@/config';

export default function LoginScreen() {
	const [email, setEmail] = useState('');
	const dispatch = useDispatch<AppDispatch>();
	const isLoading = useSelector(selectIsLoading);
	const error = useSelector(selectError);
	const theme = useTheme();

	const handleLogin = async () => {
		if (!email.trim()) {
			showAlert('Error', 'Please enter your email address');
			return;
		}

		// Basic email validation
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) {
			showAlert('Error', 'Please enter a valid email address');
			return;
		}

		try {
			// Clear any previous errors
			dispatch(clearError());

			// Attempt dev login
			const result = await dispatch(devLoginUser({ email: email.trim() }));

			if (devLoginUser.fulfilled.match(result)) {
				router.replace('/');
			} else {
				// Login failed
				const errorMessage = result.payload as string;
				showAlert('Login Failed', errorMessage);
			}
		} catch {
			showAlert('Error', 'An unexpected error occurred');
		}
	};

	const isValidEmail = (email: string) => {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	};

	const styles = createStyles(theme);

	return (
		<KeyboardAvoidingView
			style={styles.container}
			behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
		>
			<View style={styles.content}>
				<View style={styles.header}>
					<Text style={styles.title}>Welcome</Text>
					<Text style={styles.subtitle}>Enter your email to verify your account</Text>
				</View>

				<View style={styles.form}>
					<Text style={styles.label}>Email Address</Text>
					<TextInput
						style={[styles.input, error && styles.inputError]}
						placeholder='Enter your email'
						placeholderTextColor={theme.colors.icon}
						value={email}
						onChangeText={setEmail}
						keyboardType='email-address'
						autoCapitalize='none'
						autoCorrect={false}
						editable={!isLoading}
					/>

					{error && <Text style={styles.errorText}>{error}</Text>}

					<TouchableOpacity
						style={[
							styles.button,
							(!email.trim() || !isValidEmail(email) || isLoading) &&
								styles.buttonDisabled,
						]}
						onPress={handleLogin}
						disabled={!email.trim() || !isValidEmail(email) || isLoading}
					>
						{isLoading ? (
							<ActivityIndicator color={theme.colors.background} />
						) : (
							<Text style={styles.buttonText}>Verify Email</Text>
						)}
					</TouchableOpacity>

					<TouchableOpacity
						style={[styles.button, styles.secondaryButton]}
						onPress={() => router.push('/auth')}
						disabled={isLoading}
					>
						<Text style={[styles.buttonText, styles.secondaryButtonText]}>
							Login with Server Auth
						</Text>
					</TouchableOpacity>
				</View>

				<View style={styles.footer}>
					<Text style={styles.footerText}>
						{config.ENV_NAME !== 'production'
							? `You are running a ${config.ENV_NAME} build of the app.`
							: 'This is a production build of the app.'}
					</Text>
				</View>
			</View>
		</KeyboardAvoidingView>
	);
}

const createStyles = (theme: ReturnType<typeof useTheme>) =>
	StyleSheet.create({
		container: {
			flex: 1,
			backgroundColor: theme.colors.background,
		},
		content: {
			flex: 1,
			justifyContent: 'center',
			paddingHorizontal: 24,
			paddingVertical: 32,
		},
		header: {
			alignItems: 'center',
			marginBottom: 48,
		},
		title: {
			fontSize: 32,
			color: theme.colors.text,
			marginBottom: 8,
			...theme.fonts.heavy,
		},
		subtitle: {
			fontSize: 16,
			color: theme.colors.icon,
			textAlign: 'center',
			lineHeight: 24,
			...theme.fonts.regular,
		},
		form: {
			marginBottom: 32,
		},
		label: {
			fontSize: 16,
			color: theme.colors.text,
			marginBottom: 8,
			...theme.fonts.medium,
		},
		input: {
			backgroundColor: theme.colors.card,
			borderWidth: 1,
			borderColor: theme.colors.border,
			borderRadius: 8,
			paddingHorizontal: 16,
			paddingVertical: 12,
			fontSize: 16,
			marginBottom: 16,
			color: theme.colors.text,
			...theme.fonts.regular,
		},
		inputError: {
			borderColor: theme.colors.notification,
		},
		errorText: {
			color: theme.colors.notification,
			fontSize: 14,
			marginBottom: 16,
			marginTop: -8,
			...theme.fonts.regular,
		},
		button: {
			backgroundColor: theme.colors.primary,
			borderRadius: 8,
			paddingVertical: 16,
			alignItems: 'center',
			justifyContent: 'center',
			minHeight: 52,
		},
		buttonDisabled: {
			backgroundColor: theme.colors.icon,
		},
		buttonText: {
			color: theme.colors.background,
			fontSize: 16,
			...theme.fonts.medium,
		},
		secondaryButton: {
			backgroundColor: 'transparent',
			borderWidth: 1,
			borderColor: theme.colors.primary,
			marginTop: 12,
		},
		secondaryButtonText: {
			color: theme.colors.primary,
		},
		footer: {
			alignItems: 'center',
		},
		footerText: {
			fontSize: 14,
			color: theme.colors.icon,
			textAlign: 'center',
			lineHeight: 20,
			...theme.fonts.regular,
		},
	});
