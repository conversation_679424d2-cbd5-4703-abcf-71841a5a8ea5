import React, { useRef, useCallback, useMemo } from 'react';
import { View, ActivityIndicator, Text, StyleSheet, Platform } from 'react-native';
import { WebView } from 'react-native-webview';
import type { WebViewNavigation, WebViewMessageEvent } from 'react-native-webview';
import { logError } from '@/utils/errorHandler';
import authConfig, { getSupportedSchemes } from '@/config/auth';
import i18n from '@/translations';
import { AUTH_MESSAGE_TYPES } from '@/constants/auth';

/**
 * WebView error event structure
 */
interface WebViewErrorEvent {
  nativeEvent: {
    code?: number;
    description?: string;
    url?: string;
  };
}

/**
 * Theme interface for component styling
 */
interface AuthWebViewTheme {
	colors: {
		background: string;
		text: string;
		primary: string;
	};
}

/**
 * Props for the AuthWebView component
 */
interface AuthWebViewProps {
	/** URL to load in the WebView (from authService.getLoginUrl) */
	loginUrl: string;
	/** Callback when a deep link is detected */
	onDeepLink: (url: string) => void;
	/** Callback when an error occurs */
	onError?: (message: string) => void;
	/** Whether the WebView is visible */
	visible?: boolean;
	/** Theme object for styling */
	theme: AuthWebViewTheme;
}

/**
 * AuthWebView
 *
 * Encapsulates the WebView-based authentication flow:
 * - Loads the middleware login URL constructed by AuthService.getLoginUrl()
 * - Intercepts deep links using schemes derived from config.AUTH_REDIRECT_URL
 * - As a fallback, extracts the deep link from the callback page content (standard path '/sso-auth/callback')
 * - Uses i18n for UI strings (e.g., 'auth.loadingAuth')
 *
 * Configuration is loaded from environment-specific config files:
 * - Single source of truth: AUTH_REDIRECT_URL (e.g., "exp://auth-callback" or "learningcoachcommunity://auth-callback")
 */
export const AuthWebView: React.FC<AuthWebViewProps> = ({
	loginUrl,
	onDeepLink,
	onError,
	visible = true,
	theme,
}) => {
	const webviewRef = useRef<WebView>(null);

	if (!visible) {
		return null;
	}

	/**
	 * Handle WebView errors
	 */
	const handleWebViewError = useCallback((error: WebViewErrorEvent) => {
		const errorMessage = error?.nativeEvent?.description || 'WebView error occurred';
		logError('AuthWebView.error', { message: errorMessage });
		onError?.(errorMessage);
	}, [onError]);

	/**
	 * Process messages from the WebView JavaScript
	 */
	const handleWebViewMessage = useCallback((event: WebViewMessageEvent) => {
		try {
			const message = JSON.parse(event.nativeEvent.data);
			
			// Handle deep link found in page content
			if (message.type === AUTH_MESSAGE_TYPES.DEEP_LINK_FOUND && message.url) {
				if (isDeepLinkUrl(message.url)) {
					onDeepLink(message.url);
				}
			}
			
			// Handle error messages from injected JavaScript
			if (message.type === AUTH_MESSAGE_TYPES.ERROR && message.error) {
				logError('AuthWebView.jsError', { message: message.error });
			}
		} catch (error) {
			logError('AuthWebView.messageParseError', error);
		}
	}, [onDeepLink]);

	// Use the deep link pattern suffix from centralized auth config

	/**
	 * Extract JavaScript utility functions to improve readability
	 */
	const webViewJsUtils = useMemo(() => ({
		/**
		 * Generate JavaScript to extract deep links from page content
		 */
		generateDeepLinkExtractor: (schemes: string[]): string => {
			// Build regex pattern from configured schemes for JavaScript injection
			const schemePatterns = schemes.map(scheme => {
				// Escape special regex characters for JavaScript
				const escapedScheme = scheme.replace(/[.*+?^${}()|[\]\\]/g, '\\\\$&');
				return `${escapedScheme}[^\\\\s]*?${authConfig.deepLinkPatternSuffix}\\\\?[^\\\\s]+`;
			});
			const regexPattern = `(${schemePatterns.join('|')})`;

			// Create a JS-safe message type constant that doesn't rely on template interpolation
			const deepLinkFoundType = AUTH_MESSAGE_TYPES.DEEP_LINK_FOUND;
			const errorType = AUTH_MESSAGE_TYPES.ERROR;
			
			return `
        (function() {
          try {
            const bodyText = document.body ? document.body.innerText || document.body.textContent : '';
            const preText = document.querySelector('pre') ? document.querySelector('pre').innerText : '';
            const allText = bodyText + ' ' + preText;
            const deepLinkMatch = allText.match(/${regexPattern}/);
            if (deepLinkMatch) {
              window.ReactNativeWebView.postMessage(JSON.stringify({ 
                type: "${deepLinkFoundType}", 
                url: deepLinkMatch[1],
                timestamp: Date.now()
              }));
            }
          } catch (e) {
            window.ReactNativeWebView.postMessage(JSON.stringify({ 
              type: "${errorType}", 
              error: e.message,
              timestamp: Date.now() 
            }));
          }
        })();
        true;
      `;
		}
	}), []);

	/**
	 * Handle WebView navigation state changes
	 */
	const handleNavigationStateChange = useCallback((navState: WebViewNavigation) => {
		// When the callback URL finishes loading, check for deep link on the page
		if (isCallbackUrl(navState.url) && !navState.loading) {
			const schemes = getSupportedSchemes();
			const jsCode = webViewJsUtils.generateDeepLinkExtractor(schemes);
			webviewRef.current?.injectJavaScript(jsCode);
		}
	}, [webViewJsUtils]);

	/**
	 * Check if a URL is a deep link based on our auth configuration
	 */
	const isDeepLinkUrl = useCallback((url: string): boolean => {
		if (!url) return false;
		const schemes = getSupportedSchemes();
		return schemes.some(scheme => {
			const lowerUrl = url.toLowerCase();
			return lowerUrl.startsWith(scheme.toLowerCase()) && lowerUrl.includes(authConfig.deepLinkPatternSuffix);
		});
	}, []);

	/**
	 * Check if a URL is the auth callback URL
	 */
	const isCallbackUrl = useCallback((url: string): boolean => {
		if (!url) return false;
		return url.includes(authConfig.callbackPath);
	}, []);

	/**
	 * Handle URL loading and intercept deep links
	 */
	const handleShouldStartLoad = useCallback((request: WebViewNavigation) => {
		// Intercept deep link URLs and handle them as deep links instead of navigation
		if (request.url && isDeepLinkUrl(request.url)) {
			if (__DEV__) console.log('[AuthWebView] Intercepted deep link:', request.url);
			onDeepLink(request.url);
			return false; // Prevent WebView from navigating to the deep link
		}
		return true;
	}, [isDeepLinkUrl, onDeepLink]);

	const isIOS = Platform.OS === 'ios';

	return (
		<WebView
			ref={webviewRef}
			source={{ uri: loginUrl }}
			onShouldStartLoadWithRequest={handleShouldStartLoad}
			onNavigationStateChange={handleNavigationStateChange}
			onError={handleWebViewError}
			onMessage={handleWebViewMessage}
			startInLoadingState
			javaScriptEnabled
			domStorageEnabled
			sharedCookiesEnabled={isIOS} 
			thirdPartyCookiesEnabled={true} 
			cacheEnabled={true}
			incognito={false} 
			accessibilityLabel="Authentication login page"
			
			// Security configurations
			originWhitelist={[authConfig.redirectUrl, 'http://*', 'https://*']} 
			contentInset={{ top: 0, left: 0, bottom: 0, right: 0 }}
			decelerationRate="normal"
			javaScriptCanOpenWindowsAutomatically={false}
			
			renderLoading={() => (
				<View
					style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}
					accessible={true}
					accessibilityLabel="Loading authentication"
					accessibilityRole="progressbar"
				>
					<ActivityIndicator 
						size="large" 
						color={theme.colors.primary}
						accessibilityLabel="Loading indicator" 
					/>
					<Text 
						style={[styles.loadingText, { color: theme.colors.text }]}
						accessible={true}
					>
						{i18n.t('auth.loadingAuth')}
					</Text>
				</View>
			)}
		/>
	);
};

const styles = StyleSheet.create({
	loadingContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 20,
	},
	loadingText: {
		marginTop: 16,
		fontSize: 16,
		textAlign: 'center',
	},
});
