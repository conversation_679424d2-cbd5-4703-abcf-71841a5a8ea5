/**
 * Authentication Configuration
 * 
 * Centralized configuration for all authentication-related settings.
 * This serves as a single source of truth for authentication parameters
 * across the mobile app.
 */

import config from './index';
import { Platform } from 'react-native';

/**
 * Authentication endpoints configuration
 */
export interface AuthEndpoints {
  login: string;
  logout: string;
  callback: string;
  tokenExchange: string;
  userProfile: string;
}

/**
 * Complete authentication configuration
 */
export interface AuthConfig {
  // Deep linking configuration
  redirectUrl: string;
  callbackPath: string;
  deepLinkPatternSuffix: string;
  
  // Session management
  tokenExpiryMs: number;
  
  // API paths (relative to base URL)
  endpoints: AuthEndpoints;
  
  // Platform-specific adjustments
  getAdjustedBaseUrl(url: string): string;
}

/**
 * Default authentication configuration
 */
const authConfig: AuthConfig = {
  // Deep linking configuration from app.config.json
  redirectUrl: config.AUTH_REDIRECT_URL,
  callbackPath: '/sso-auth/callback',
  deepLinkPatternSuffix: 'auth-callback',
  
  // Session management
  tokenExpiryMs: 60 * 60 * 1000, // 1 hour by default
  
  // API endpoints (relative paths)
  endpoints: {
    login: '/api/auth/login',
    logout: '/api/auth/logout',
    callback: '/sso-auth/callback',
    tokenExchange: '/api/auth/exchange-token',
    userProfile: '/api/user/me'
  },
  
  // Platform-specific URL adjustments
  getAdjustedBaseUrl(baseUrl: string): string {
    // For Android emulators, replace localhost with ********
    if (Platform.OS === 'android' && baseUrl.includes('localhost')) {
      return baseUrl.replace('localhost', '********');
    }
    
    // For iOS, keep as is
    return baseUrl;
  }
};

/**
 * Extract URL scheme from the redirect URL
 * @returns Array of supported URL schemes
 */
export function getSupportedSchemes(): string[] {
  try {
    const u = new URL(authConfig.redirectUrl);
    // URL.protocol includes trailing ':'
    const scheme = u.protocol.replace(':', '');
    return [`${scheme}://`];
  } catch {
    // If parsing fails, return empty to avoid accidental matches
    return [];
  }
}

/**
 * Get origin from a URL
 * @param url Full URL
 * @returns Just the origin part (protocol + host)
 */
export function getOriginFromUrl(url: string): string {
  try {
    const u = new URL(url);
    return `${u.protocol}//${u.host}`;
  } catch {
    // Fallback: best-effort origin extraction
    const m = url.match(/^((https?:)\/\/[^\/]+)\//i);
    return m?.[1] || '';
  }
}

export default authConfig;
